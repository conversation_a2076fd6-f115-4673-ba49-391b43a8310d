package org.kefir.services

class PackageInfoService {
    private final def script

    PackageInfoService(script) {
        this.script = script
    }

    /**
     * Extracts package information from package.json in the current workspace
     * @return Map containing package name and version
     */
    Map getPackageInfo() {
        try {
            if (script.fileExists('package.json')) {
                def packageJson = script.readJSON(file: 'package.json')
                return [
                    name: packageJson.name ?: '',
                    version: packageJson.version ?: ''
                ]
            } else {
                script.echo "package.json not found in workspace"
                return [name: '', version: '']
            }
        } catch (Exception e) {
            script.echo "Error reading package.json: ${e.message}"
            return [name: '', version: '']
        }
    }

    /**
     * Validates that package.json contains required fields
     * @return Boolean indicating if package info is valid
     */
    Boolean isPackageInfoValid() {
        def packageInfo = getPackageInfo()
        return packageInfo.name && packageInfo.version
    }
}
