package org.kefir.services

import org.kefir.configs.MattermostConstants

class MattermostService {
    private final def script
    private final GitlabService gitlab
    private final PackageInfoService packageInfoService

    MattermostService(script) {
        this.script = script
        this.gitlab = new GitlabService(script)
        this.packageInfoService = new PackageInfoService(script)
    }

    /**
     * Sends a notification about successful package publication
     * @param config Map containing notification configuration
     * @return Boolean indicating success
     */
    Boolean sendPackagePublishedNotification(Map config) {
        try {
            def packageInfo = packageInfoService.getPackageInfo()

            if (!packageInfo.name || !packageInfo.version) {
                script.echo "Warning: Could not extract package name or version from package.json"
                return false
            }

            String message = formatPackagePublishedMessage(packageInfo, config)

            return script.mattermostSend(
                channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
                color: MattermostConstants.COLOR_GOOD,
                message: message
            )
        } catch (Exception e) {
            script.echo "Error sending Mattermost notification: ${e.message}"
            return false
        }
    }

    /**
     * Sends a custom message to Mattermost
     * @param config Map containing message configuration
     * @return Boolean indicating success
     */
    Boolean sendCustomMessage(Map config) {
        try {
            return script.mattermostSend(
                channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
                color: config.color ?: MattermostConstants.COLOR_GOOD,
                message: config.message
            )
        } catch (Exception e) {
            script.echo "Error sending custom Mattermost message: ${e.message}"
            return false
        }
    }

    /**
     * Sends a short status message to Mattermost
     * @param config Map containing status configuration
     * @return Boolean indicating success
     */
    Boolean sendShortStatus(Map config) {
        try {
            return script.mattermostSend(
                channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
                message: config.message
            )
        } catch (Exception e) {
            script.echo "Error sending Mattermost status: ${e.message}"
            return false
        }
    }



    /**
     * Formats the message for package publication notification
     * @param packageInfo Map containing package name and version
     * @param config Map containing notification configuration
     * @return String formatted message
     */
    private String formatPackagePublishedMessage(Map packageInfo, Map config) {
        String message = String.format(MattermostConstants.PACKAGE_PUBLISHED_TEMPLATE, packageInfo.name, packageInfo.version) + "\n\n"

        message += "${MattermostConstants.BUILD_INFO_HEADER}\n"
        message += "• Job: [${script.env.JOB_NAME}](${script.env.JOB_URL})\n"
        message += "• Build: [#${script.env.BUILD_NUMBER}](${script.env.BUILD_URL})\n"

        if (script.env.BRANCH_NAME) {
            message += "• Branch: ${script.env.BRANCH_NAME}\n"
        }

        if (config.message) {
            message += "\n${config.message}\n"
        }

        return message
    }
}
