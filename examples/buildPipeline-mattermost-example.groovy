@Library('devops-shared') _

/**
 * Example usage of buildPipeline with Mattermost notifications
 * This example demonstrates how to configure Mattermost notifications
 * for npm package builds that include package name and version
 */

// Basic configuration with Mattermost notifications
def config = [
    agentLabel: 'gce',
    
    // NPM package publishing configuration
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio',
        nodeJsTool: 'node24.3.0'
    ],
    
    // Documentation publishing (optional)
    docs: [
        enabled: false
    ],
    
    // Mattermost notification configuration
    notifications: [
        enabled: true,                    // Enable notifications
        channel: '#npm-releases',         // Target Mattermost channel
        message: 'Package successfully published to Verdaccio!' // Optional custom message
    ]
]

// Call buildPipeline with the configuration
buildPipeline(config)

/*
 * Expected notification format when package is successfully built:
 * 
 * 📦 **Package com.kefir.my-awesome-package has been published to version 1.2.3**
 * 
 * **Build Information:**
 * • Job: [my-project/master](http://jenkins.example.com/job/my-project/job/master/)
 * • Build: [#42](http://jenkins.example.com/job/my-project/job/master/42/)
 * • Branch: master
 * 
 * Package successfully published to Verdaccio!
 */

// Alternative configuration with different channel
def alternativeConfig = [
    agentLabel: 'docker',
    
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio-prod',
        nodeJsTool: 'node24.3.0'
    ],
    
    docs: [
        enabled: true,
        confluenceSpaceName: 'DEV',
        confluenceParentPageID: '12345',
        documentationDirectoryPath: 'docs',
        documentationLabel: 'my-package',
        documentationTitle: 'My Package Documentation',
        documentationHomePagePath: 'docs/README.md'
    ],
    
    notifications: [
        enabled: true,
        channel: '#deployment-notifications'
        // No custom message - will use default format
    ]
]

// Uncomment to use alternative configuration
// buildPipeline(alternativeConfig)

/*
 * Configuration options for notifications:
 * 
 * notifications: [
 *     enabled: true,                    // Required: Enable/disable notifications
 *     channel: '#channel-name',         // Optional: Target channel (default: #jenkins)
 *     message: 'Custom message'         // Optional: Additional message to include
 * ]
 * 
 * Requirements:
 * - The workspace must contain a valid package.json file
 * - The package.json must have 'name' and 'version' fields
 * - Jenkins must have Mattermost plugin configured
 * - The Jenkins bot must have access to the specified channel
 */
