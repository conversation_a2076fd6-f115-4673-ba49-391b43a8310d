import org.kefir.services.DocBuilder
import org.kefir.services.NpmService

def call(Map config) {
    def npmService = new NpmService(this)
    def docBuilder = new DocBuilder(this)

    pipeline {
        agent { node { label config.agentLabel ?: jenkinsUtils.determineAgent() } }

        stages {
            stage('Публикация приложения') {
                when {
                    expression { config.app.enabled }
                }
                steps {
                    script {
                        npmService.publishNpm([
                            verdaccioInstance: config.app.verdaccioInstance,
                            nodeJsTool: config.app.nodeJsTool
                        ])
                    }
                }
            }
            stage('Публикация документации в Confluence') {
                when {
                    expression { config.docs.enabled }
                }
                steps {
                    script {
                        docBuilder.publishDocs([
                            confluenceSpaceName: config.docs.confluenceSpaceName,
                            confluenceParentPageID: config.docs.confluenceParentPageID,
                            documentationDirectoryPath: config.docs.documentationDirectoryPath,
                            documentationLabel: config.docs.documentationLabel,
                            documentationTitle: config.docs.documentationTitle,
                            documentationHomePagePath: config.docs.documentationHomePagePath
                        ])
                    }
                }
            }
        }

        post {
            success {
                script {
                    if (config.notifications?.enabled && config.app?.enabled) {
                        mattermostUtils.packagePublished([
                            channel: config.notifications.channel,
                            message: config.notifications.message
                        ])
                    }
                }
            }
            failure {
                script {
                    if (config.notifications?.enabled) {
                        mattermostUtils.buildFailed([
                            channel: config.notifications.channel,
                            error: [message: currentBuild.description ?: 'Build failed during pipeline execution']
                        ])
                    }
                }
            }
        }
    }
}