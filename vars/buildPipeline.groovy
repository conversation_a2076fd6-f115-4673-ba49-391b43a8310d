import org.kefir.services.DocBuilder
import org.kefir.services.NpmService
import org.kefir.services.MattermostService

def call(Map config) {
    def npmService = new NpmService(this)
    def docBuilder = new DocBuilder(this)
    def mattermostService = new MattermostService(this)

    pipeline {
        agent { node { label config.agentLabel ?: jenkinsUtils.determineAgent() } }

        stages {
            stage('Публикация приложения') {
                when {
                    expression { config.app.enabled }
                }
                steps {
                    script {
                        npmService.publishNpm([
                            verdaccioInstance: config.app.verdaccioInstance,
                            nodeJsTool: config.app.nodeJsTool
                        ])
                    }
                }
            }
            stage('Публикация документации в Confluence') {
                when {
                    expression { config.docs.enabled }
                }
                steps {
                    script {
                        docBuilder.publishDocs([
                            confluenceSpaceName: config.docs.confluenceSpaceName,
                            confluenceParentPageID: config.docs.confluenceParentPageID,
                            documentationDirectoryPath: config.docs.documentationDirectoryPath,
                            documentationLabel: config.docs.documentationLabel,
                            documentationTitle: config.docs.documentationTitle,
                            documentationHomePagePath: config.docs.documentationHomePagePath
                        ])
                    }
                }
            }
        }

        post {
            success {
                script {
                    // Send Mattermost notification if enabled and app was published
                    if (config.notifications?.enabled && config.app?.enabled) {
                        mattermostService.sendPackagePublishedNotification([
                            channel: config.notifications.channel ?: '#jenkins',
                            message: config.notifications.message
                        ])
                    }
                }
            }
        }
    }
}